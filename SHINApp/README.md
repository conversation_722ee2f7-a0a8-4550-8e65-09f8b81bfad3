# SHIN Mobile App

A comprehensive React Native mobile application for residential building management, designed for residents to manage their building-related activities, payments, communications, and more.

## Features

### 🔐 Authentication
- Phone number-based login with country code detection
- 4-digit SMS verification code
- Auto-advance input fields and paste support
- Session management with automatic logout

### 🏠 Resident Dashboard
- Welcome screen with personalized greeting
- Quick access to all building services
- Badge notifications for unread items
- Status indicators for payments and utilities

### 🔑 Elevator Access
- Current elevator codes display
- Multiple entrance support
- Copy-to-clipboard functionality
- Auto-refresh with last updated timestamps

### 💰 Financial Management
- **Maintenance Fees**: View current fees, payment status, and progress tracking
- **Piggy Bank**: Building savings and expense history
- Payment processing integration ready
- Visual progress bars and status badges

### 👤 Account Management
- Editable profile information (name/initials)
- Read-only building details (entrance, apartment)
- Family member management
- Secure logout with confirmation

### 🏢 Building Information
- Complete building address with map integration
- Contact directory (chairman, security, support)
- Personal notes (local storage)
- Building rules and facilities info

### 💬 Communication (Coming Soon)
- Entrance-specific chat rooms
- Building-wide announcements
- Mention system (@chairman)
- Push notification integration

### 🗳️ Voting System (Coming Soon)
- Active and past votes display
- One vote per apartment restriction
- Comment system for votes
- Real-time results

### 📋 Request Management (Coming Soon)
- Submit maintenance requests
- Track request status
- Image attachments
- Status updates

### 🌐 Multi-language Support
- English, Georgian (ქართული), Russian (Русский)
- Instant language switching
- Persistent language preferences

### 🎨 Theme Support
- Light and dark mode
- System theme detection
- Smooth theme transitions
- Persistent theme preferences

### 📱 Offline Support
- Cached data for offline viewing
- Connection status indicators
- Sync when back online
- Essential data always available

## Tech Stack

### Core Framework
- **React Native 0.73.2** - Cross-platform mobile development
- **TypeScript** - Type-safe development
- **Metro** - React Native bundler

### Navigation & State Management
- **React Navigation v6** - Navigation library
- **Redux Toolkit** - State management
- **React Redux** - React bindings for Redux
- **AsyncStorage** - Local data persistence

### UI & Styling
- **NativeWind** - Tailwind CSS for React Native
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React Native** - Beautiful icons
- **React Native Reanimated** - Smooth animations
- **React Native Gesture Handler** - Touch gestures

### Additional Libraries
- **React Native Safe Area Context** - Safe area handling
- **React Native Screens** - Native screen optimization
- **React Native Toast Message** - Toast notifications
- **Lottie React Native** - Animations
- **React Native Clipboard** - Clipboard operations
- **React Native SVG** - SVG support

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Button, Input, Card)
│   ├── common/         # Shared app components
│   └── forms/          # Form-specific components
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   ├── dashboard/      # Dashboard screens
│   ├── account/        # Account management
│   ├── elevator/       # Elevator code screens
│   ├── building/       # Building info screens
│   ├── financials/     # Financial screens
│   ├── chat/           # Chat screens
│   ├── voting/         # Voting screens
│   └── todo/           # Request management
├── navigation/         # Navigation configuration
├── store/              # Redux store and slices
├── contexts/           # React contexts (Theme, Language)
├── constants/          # App constants and translations
├── hooks/              # Custom React hooks
├── services/           # API services
├── utils/              # Utility functions
└── types/              # TypeScript type definitions
```

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- React Native development environment
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd SHINApp
```

2. Install dependencies:
```bash
npm install --legacy-peer-deps
```

3. Install iOS dependencies (iOS only):
```bash
cd ios && pod install && cd ..
```

### Running the App

#### iOS
```bash
npm run ios
```

#### Android
```bash
npm run android
```

#### Start Metro Bundler
```bash
npm start
```

### Development Scripts

- `npm start` - Start Metro bundler
- `npm run ios` - Run on iOS simulator
- `npm run android` - Run on Android emulator
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run typecheck` - Run TypeScript type checking

## Configuration

### Theme Customization
Edit `tailwind.config.js` to customize colors, fonts, and spacing:

```javascript
theme: {
  extend: {
    colors: {
      primary: { /* your primary colors */ },
      secondary: { /* your secondary colors */ },
    }
  }
}
```

### Language Support
Add new languages in `src/constants/translations.ts`:

```typescript
export const translations = {
  en: { /* English translations */ },
  ka: { /* Georgian translations */ },
  ru: { /* Russian translations */ },
  // Add new languages here
};
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
