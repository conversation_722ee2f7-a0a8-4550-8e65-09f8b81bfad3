import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  phoneNumber: string | null;
  verificationCode: string | null;
  token: string | null;
  error: string | null;
  resendCooldown: number;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  phoneNumber: null,
  verificationCode: null,
  token: null,
  error: null,
  resendCooldown: 0,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setPhoneNumber: (state, action: PayloadAction<string>) => {
      state.phoneNumber = action.payload;
      state.error = null;
    },
    setVerificationCode: (state, action: PayloadAction<string>) => {
      state.verificationCode = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setResendCooldown: (state, action: PayloadAction<number>) => {
      state.resendCooldown = action.payload;
    },
    loginSuccess: (state, action: PayloadAction<string>) => {
      state.isAuthenticated = true;
      state.token = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.phoneNumber = null;
      state.verificationCode = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setPhoneNumber,
  setVerificationCode,
  setError,
  setResendCooldown,
  loginSuccess,
  logout,
  clearError,
} = authSlice.actions;

export default authSlice.reducer;
