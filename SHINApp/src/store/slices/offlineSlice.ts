import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface OfflineState {
  isConnected: boolean;
  lastSyncTime: string | null;
  pendingActions: any[];
}

const initialState: OfflineState = {
  isConnected: true,
  lastSyncTime: null,
  pendingActions: [],
};

const offlineSlice = createSlice({
  name: 'offline',
  initialState,
  reducers: {
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    setLastSyncTime: (state, action: PayloadAction<string>) => {
      state.lastSyncTime = action.payload;
    },
    addPendingAction: (state, action: PayloadAction<any>) => {
      state.pendingActions.push(action.payload);
    },
    clearPendingActions: (state) => {
      state.pendingActions = [];
    },
  },
});

export const {
  setConnectionStatus,
  setLastSyncTime,
  addPendingAction,
  clearPendingActions,
} = offlineSlice.actions;

export default offlineSlice.reducer;
