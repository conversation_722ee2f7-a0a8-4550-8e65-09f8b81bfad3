import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
}

interface UserProfile {
  id: string;
  name: string;
  phoneNumber: string;
  entrance: string;
  apartment: string;
  familyMembers: FamilyMember[];
}

interface UserState {
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  profile: null,
  isLoading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setProfile: (state, action: PayloadAction<UserProfile>) => {
      state.profile = action.payload;
      state.error = null;
    },
    updateName: (state, action: PayloadAction<string>) => {
      if (state.profile) {
        state.profile.name = action.payload;
      }
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearProfile: (state) => {
      state.profile = null;
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setProfile,
  updateName,
  setError,
  clearProfile,
} = userSlice.actions;

export default userSlice.reducer;
