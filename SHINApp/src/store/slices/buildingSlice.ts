import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface ElevatorCode {
  id: string;
  code: string;
  entrance: string;
  lastUpdated: string;
}

interface MaintenanceFee {
  id: string;
  amount: number;
  dueDate: string;
  isPaid: boolean;
  goal: number;
  currentAmount: number;
}

interface Expense {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
}

interface BuildingInfo {
  id: string;
  name: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  notes: string;
}

interface Contact {
  id: string;
  name: string;
  role: string;
  phoneNumber: string;
}

interface BuildingState {
  elevatorCodes: ElevatorCode[];
  maintenanceFee: MaintenanceFee | null;
  piggyBankTotal: number;
  expenses: Expense[];
  buildingInfo: BuildingInfo | null;
  contacts: Contact[];
  isLoading: boolean;
  error: string | null;
}

const initialState: BuildingState = {
  elevatorCodes: [],
  maintenanceFee: null,
  piggyBankTotal: 0,
  expenses: [],
  buildingInfo: null,
  contacts: [],
  isLoading: false,
  error: null,
};

const buildingSlice = createSlice({
  name: 'building',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setElevatorCodes: (state, action: PayloadAction<ElevatorCode[]>) => {
      state.elevatorCodes = action.payload;
    },
    setMaintenanceFee: (state, action: PayloadAction<MaintenanceFee>) => {
      state.maintenanceFee = action.payload;
    },
    setPiggyBankTotal: (state, action: PayloadAction<number>) => {
      state.piggyBankTotal = action.payload;
    },
    setExpenses: (state, action: PayloadAction<Expense[]>) => {
      state.expenses = action.payload;
    },
    setBuildingInfo: (state, action: PayloadAction<BuildingInfo>) => {
      state.buildingInfo = action.payload;
    },
    updateBuildingNotes: (state, action: PayloadAction<string>) => {
      if (state.buildingInfo) {
        state.buildingInfo.notes = action.payload;
      }
    },
    setContacts: (state, action: PayloadAction<Contact[]>) => {
      state.contacts = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setLoading,
  setElevatorCodes,
  setMaintenanceFee,
  setPiggyBankTotal,
  setExpenses,
  setBuildingInfo,
  updateBuildingNotes,
  setContacts,
  setError,
} = buildingSlice.actions;

export default buildingSlice.reducer;
