import React, {useState} from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';

import {useTheme} from '../../contexts/ThemeContext';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  leftIcon,
  rightIcon,
  ...textInputProps
}) => {
  const {colors} = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const containerStyles: ViewStyle = {
    marginBottom: 16,
    ...containerStyle,
  };

  const labelStyles: TextStyle = {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
    ...labelStyle,
  };

  const inputContainerStyles: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: error ? colors.error : isFocused ? colors.primary : colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
    paddingHorizontal: 16,
    minHeight: 48,
  };

  const inputStyles: TextStyle = {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    paddingVertical: 12,
    ...inputStyle,
  };

  const errorStyles: TextStyle = {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
    ...errorStyle,
  };

  return (
    <View style={containerStyles}>
      {label && <Text style={labelStyles}>{label}</Text>}
      <View style={inputContainerStyles}>
        {leftIcon && <View style={{marginRight: 12}}>{leftIcon}</View>}
        <TextInput
          style={inputStyles}
          placeholderTextColor={colors.textSecondary}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...textInputProps}
        />
        {rightIcon && <View style={{marginLeft: 12}}>{rightIcon}</View>}
      </View>
      {error && <Text style={errorStyles}>{error}</Text>}
    </View>
  );
};
