export const translations = {
  en: {
    common: {
      save: 'Save',
      cancel: 'Cancel',
      edit: 'Edit',
      delete: 'Delete',
      confirm: 'Confirm',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      retry: 'Retry',
      refresh: 'Refresh',
      copy: 'Copy',
      copied: 'Copied!',
      offline: 'You are offline',
      back: 'Back',
      next: 'Next',
      done: 'Done',
    },
    auth: {
      phoneLogin: 'Phone Login',
      enterPhone: 'Enter your phone number',
      phoneNumber: 'Phone Number',
      sendCode: 'Send Code',
      verificationCode: 'Verification Code',
      enterCode: 'Enter the 4-digit code sent to your phone',
      resendCode: 'Resend Code',
      resendIn: 'Resend in',
      seconds: 'seconds',
      invalidPhone: 'Invalid phone number',
      invalidCode: 'Invalid verification code',
      codeExpired: 'Code expired',
      tooManyAttempts: 'Too many attempts. Please try again later.',
    },
    dashboard: {
      welcome: 'Welcome',
      announcements: 'Announcements',
      activeVotes: 'Active Votes',
      unreadMessages: 'Unread Messages',
      paymentStatus: 'Payment Status',
      utilityOutages: 'Utility Outages',
      elevatorCode: 'Elevator Code',
      buildingInfo: 'Building Info',
      maintenanceFee: 'Maintenance Fee',
      piggyBank: 'Piggy Bank',
      chats: 'Chats',
      voting: 'Voting',
      account: 'Account',
    },
    account: {
      settings: 'Account Settings',
      profile: 'Profile',
      name: 'Name',
      entrance: 'Entrance',
      apartment: 'Apartment',
      familyMembers: 'Family Members',
      logout: 'Logout',
      logoutConfirm: 'Are you sure you want to logout?',
      saveChanges: 'Save Changes',
      changesSaved: 'Changes saved successfully',
    },
    building: {
      info: 'Building Information',
      address: 'Address',
      notes: 'Notes',
      contacts: 'Contacts',
      chairman: 'Chairman',
      security: 'Security',
      support: 'Support',
      facilities: 'Facilities',
      rules: 'Rules',
      sustainability: 'Sustainability',
      faq: 'FAQ',
      searchFaq: 'Search FAQ...',
    },
    elevator: {
      code: 'Elevator Code',
      currentCode: 'Current Code',
      copyCode: 'Copy Code',
      multipleEntrances: 'Multiple Entrances',
      lastUpdated: 'Last Updated',
    },
    financials: {
      maintenanceFee: 'Maintenance Fee',
      currentGoal: 'Current Goal',
      progress: 'Progress',
      expenses: 'Expenses',
      payNow: 'Pay Now',
      paymentHistory: 'Payment History',
      piggyBank: 'Piggy Bank',
      totalSaved: 'Total Saved',
      buildingExpenses: 'Building Expenses',
      paid: 'Paid',
      unpaid: 'Unpaid',
      overdue: 'Overdue',
    },
    chat: {
      entranceChat: 'Entrance Chat',
      buildingChat: 'Building Chat',
      typeMessage: 'Type a message...',
      send: 'Send',
      mentionChairman: 'Mention Chairman',
    },
    voting: {
      activeVotes: 'Active Votes',
      pastVotes: 'Past Votes',
      vote: 'Vote',
      results: 'Results',
      comments: 'Comments',
      addComment: 'Add Comment',
      oneVotePerApartment: 'One vote per apartment',
      votingClosed: 'Voting Closed',
    },
    todo: {
      myRequests: 'My Requests',
      submitRequest: 'Submit Request',
      title: 'Title',
      description: 'Description',
      attachImages: 'Attach Images',
      submit: 'Submit',
      pending: 'Pending',
      inProgress: 'In Progress',
      completed: 'Completed',
    },
    settings: {
      language: 'Language',
      theme: 'Theme',
      light: 'Light',
      dark: 'Dark',
      notifications: 'Notifications',
      about: 'About',
    },
  },
  ka: {
    // Georgian translations would go here
    common: {
      save: 'შენახვა',
      cancel: 'გაუქმება',
      edit: 'რედაქტირება',
      delete: 'წაშლა',
      confirm: 'დადასტურება',
      loading: 'იტვირთება...',
      error: 'შეცდომა',
      success: 'წარმატება',
      retry: 'ხელახლა ცდა',
      refresh: 'განახლება',
      copy: 'კოპირება',
      copied: 'კოპირებულია!',
      offline: 'თქვენ ოფლაინ ხართ',
      back: 'უკან',
      next: 'შემდეგი',
      done: 'დასრულებული',
    },
    // ... other Georgian translations
  },
  ru: {
    // Russian translations would go here
    common: {
      save: 'Сохранить',
      cancel: 'Отмена',
      edit: 'Редактировать',
      delete: 'Удалить',
      confirm: 'Подтвердить',
      loading: 'Загрузка...',
      error: 'Ошибка',
      success: 'Успех',
      retry: 'Повторить',
      refresh: 'Обновить',
      copy: 'Копировать',
      copied: 'Скопировано!',
      offline: 'Вы не в сети',
      back: 'Назад',
      next: 'Далее',
      done: 'Готово',
    },
    // ... other Russian translations
  },
};
