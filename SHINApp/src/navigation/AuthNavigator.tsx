import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import {PhoneLoginScreen} from '../screens/auth/PhoneLoginScreen';
import {VerificationCodeScreen} from '../screens/auth/VerificationCodeScreen';
import {LanguageThemeSetupScreen} from '../screens/auth/LanguageThemeSetupScreen';

export type AuthStackParamList = {
  LanguageThemeSetup: undefined;
  PhoneLogin: undefined;
  VerificationCode: {phoneNumber: string};
};

const Stack = createStackNavigator<AuthStackParamList>();

export const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="LanguageThemeSetup"
      screenOptions={{
        headerShown: false,
        cardStyle: {backgroundColor: 'transparent'},
      }}>
      <Stack.Screen 
        name="LanguageThemeSetup" 
        component={LanguageThemeSetupScreen} 
      />
      <Stack.Screen 
        name="PhoneLogin" 
        component={PhoneLoginScreen} 
      />
      <Stack.Screen 
        name="VerificationCode" 
        component={VerificationCodeScreen} 
      />
    </Stack.Navigator>
  );
};
