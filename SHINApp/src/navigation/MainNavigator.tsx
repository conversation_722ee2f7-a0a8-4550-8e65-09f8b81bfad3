import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import {Home, Building, MessageCircle, Vote, User} from 'lucide-react-native';

import {useTheme} from '../contexts/ThemeContext';
import {ResidentDashboardScreen} from '../screens/dashboard/ResidentDashboardScreen';
import {BuildingInfoScreen} from '../screens/building/BuildingInfoScreen';
import {ChatsScreen} from '../screens/chat/ChatsScreen';
import {VotingScreen} from '../screens/voting/VotingScreen';
import {AccountSettingsScreen} from '../screens/account/AccountSettingsScreen';
import {ElevatorCodeScreen} from '../screens/elevator/ElevatorCodeScreen';
import {MaintenanceFeeScreen} from '../screens/financials/MaintenanceFeeScreen';
import {PiggyBankScreen} from '../screens/financials/PiggyBankScreen';
import {TodoListScreen} from '../screens/todo/TodoListScreen';

export type MainTabParamList = {
  Dashboard: undefined;
  Building: undefined;
  Chats: undefined;
  Voting: undefined;
  Account: undefined;
};

export type MainStackParamList = {
  MainTabs: undefined;
  ElevatorCode: undefined;
  MaintenanceFee: undefined;
  PiggyBank: undefined;
  TodoList: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();
const Stack = createStackNavigator<MainStackParamList>();

const MainTabs: React.FC = () => {
  const {colors} = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, size}) => {
          const iconColor = focused ? colors.primary : colors.textSecondary;
          
          switch (route.name) {
            case 'Dashboard':
              return <Home size={size} color={iconColor} />;
            case 'Building':
              return <Building size={size} color={iconColor} />;
            case 'Chats':
              return <MessageCircle size={size} color={iconColor} />;
            case 'Voting':
              return <Vote size={size} color={iconColor} />;
            case 'Account':
              return <User size={size} color={iconColor} />;
            default:
              return null;
          }
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
        },
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.text,
      })}>
      <Tab.Screen 
        name="Dashboard" 
        component={ResidentDashboardScreen}
        options={{title: 'Home'}}
      />
      <Tab.Screen 
        name="Building" 
        component={BuildingInfoScreen}
        options={{title: 'Building'}}
      />
      <Tab.Screen 
        name="Chats" 
        component={ChatsScreen}
        options={{title: 'Chats'}}
      />
      <Tab.Screen 
        name="Voting" 
        component={VotingScreen}
        options={{title: 'Voting'}}
      />
      <Tab.Screen 
        name="Account" 
        component={AccountSettingsScreen}
        options={{title: 'Account'}}
      />
    </Tab.Navigator>
  );
};

export const MainNavigator: React.FC = () => {
  const {colors} = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: '600',
        },
      }}>
      <Stack.Screen 
        name="MainTabs" 
        component={MainTabs}
        options={{headerShown: false}}
      />
      <Stack.Screen 
        name="ElevatorCode" 
        component={ElevatorCodeScreen}
        options={{title: 'Elevator Code'}}
      />
      <Stack.Screen 
        name="MaintenanceFee" 
        component={MaintenanceFeeScreen}
        options={{title: 'Maintenance Fee'}}
      />
      <Stack.Screen 
        name="PiggyBank" 
        component={PiggyBankScreen}
        options={{title: 'Piggy Bank'}}
      />
      <Stack.Screen 
        name="TodoList" 
        component={TodoListScreen}
        options={{title: 'My Requests'}}
      />
    </Stack.Navigator>
  );
};
