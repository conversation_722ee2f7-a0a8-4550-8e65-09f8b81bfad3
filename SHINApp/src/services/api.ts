import {ApiResponse, PaginatedResponse} from '../types';

// Base API configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://api.shinapp.com';

const DEFAULT_TIMEOUT = 10000;

// API client class
class ApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string = API_BASE_URL, timeout: number = DEFAULT_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  // Set authentication token
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  // Remove authentication token
  removeAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
      signal: controller.signal,
    };

    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);
      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || `HTTP ${response.status}`,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error: any) {
      clearTimeout(timeoutId);
      return {
        success: false,
        error: error.message || 'Network error',
      };
    }
  }

  // HTTP methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {method: 'GET'});
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {method: 'DELETE'});
  }

  // Upload file
  async upload<T>(endpoint: string, file: FormData): Promise<ApiResponse<T>> {
    const headers = {...this.defaultHeaders};
    delete headers['Content-Type']; // Let browser set it for FormData

    return this.request<T>(endpoint, {
      method: 'POST',
      body: file,
      headers,
    });
  }
}

// Create API client instance
export const apiClient = new ApiClient();

// Authentication API
export const authApi = {
  sendVerificationCode: (phoneNumber: string) =>
    apiClient.post('/auth/send-code', {phoneNumber}),
  
  verifyCode: (phoneNumber: string, code: string) =>
    apiClient.post('/auth/verify-code', {phoneNumber, code}),
  
  refreshToken: (refreshToken: string) =>
    apiClient.post('/auth/refresh', {refreshToken}),
  
  logout: () =>
    apiClient.post('/auth/logout'),
};

// User API
export const userApi = {
  getProfile: () =>
    apiClient.get('/user/profile'),
  
  updateProfile: (data: any) =>
    apiClient.patch('/user/profile', data),
  
  getFamilyMembers: () =>
    apiClient.get('/user/family-members'),
};

// Building API
export const buildingApi = {
  getInfo: () =>
    apiClient.get('/building/info'),
  
  getElevatorCodes: () =>
    apiClient.get('/building/elevator-codes'),
  
  getContacts: () =>
    apiClient.get('/building/contacts'),
  
  updateNotes: (notes: string) =>
    apiClient.patch('/building/notes', {notes}),
};

// Financial API
export const financialApi = {
  getMaintenanceFee: () =>
    apiClient.get('/financial/maintenance-fee'),
  
  getPiggyBank: () =>
    apiClient.get('/financial/piggy-bank'),
  
  getExpenses: (page = 1, limit = 20) =>
    apiClient.get<PaginatedResponse<any>>(`/financial/expenses?page=${page}&limit=${limit}`),
  
  payMaintenanceFee: (amount: number) =>
    apiClient.post('/financial/pay-maintenance-fee', {amount}),
};

// Chat API
export const chatApi = {
  getRooms: () =>
    apiClient.get('/chat/rooms'),
  
  getMessages: (roomId: string, page = 1, limit = 50) =>
    apiClient.get(`/chat/rooms/${roomId}/messages?page=${page}&limit=${limit}`),
  
  sendMessage: (roomId: string, content: string, mentions?: string[]) =>
    apiClient.post(`/chat/rooms/${roomId}/messages`, {content, mentions}),
  
  markAsRead: (roomId: string) =>
    apiClient.post(`/chat/rooms/${roomId}/mark-read`),
};

// Voting API
export const votingApi = {
  getActiveVotes: () =>
    apiClient.get('/voting/active'),
  
  getPastVotes: (page = 1, limit = 20) =>
    apiClient.get<PaginatedResponse<any>>(`/voting/past?page=${page}&limit=${limit}`),
  
  castVote: (voteId: string, optionId: string, comment?: string) =>
    apiClient.post(`/voting/${voteId}/cast`, {optionId, comment}),
  
  getVoteResults: (voteId: string) =>
    apiClient.get(`/voting/${voteId}/results`),
};

// Todo/Request API
export const todoApi = {
  getRequests: (status?: string) =>
    apiClient.get(`/todo/requests${status ? `?status=${status}` : ''}`),
  
  createRequest: (data: any) =>
    apiClient.post('/todo/requests', data),
  
  updateRequest: (id: string, data: any) =>
    apiClient.patch(`/todo/requests/${id}`, data),
  
  deleteRequest: (id: string) =>
    apiClient.delete(`/todo/requests/${id}`),
  
  addComment: (requestId: string, content: string) =>
    apiClient.post(`/todo/requests/${requestId}/comments`, {content}),
};

// Notification API
export const notificationApi = {
  getNotifications: (page = 1, limit = 20) =>
    apiClient.get<PaginatedResponse<any>>(`/notifications?page=${page}&limit=${limit}`),
  
  markAsRead: (notificationId: string) =>
    apiClient.patch(`/notifications/${notificationId}/read`),
  
  markAllAsRead: () =>
    apiClient.post('/notifications/mark-all-read'),
  
  updatePushToken: (token: string) =>
    apiClient.post('/notifications/push-token', {token}),
};

// Utility functions
export const handleApiError = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.error) return error.error;
  return 'An unexpected error occurred';
};

export const isNetworkError = (error: any): boolean => {
  return error?.message?.includes('Network') || 
         error?.message?.includes('fetch') ||
         error?.message?.includes('timeout');
};

// Mock data for development
export const mockApi = {
  // Mock responses for development/testing
  sendVerificationCode: async (phoneNumber: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {success: true, data: {message: 'Code sent successfully'}};
  },
  
  verifyCode: async (phoneNumber: string, code: string) => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    if (code === '0000') {
      return {success: false, error: 'Invalid verification code'};
    }
    return {
      success: true, 
      data: {
        token: 'mock-jwt-token',
        user: {
          id: '1',
          name: 'John Doe',
          phoneNumber,
          entrance: '2',
          apartment: '4B',
        }
      }
    };
  },
};

// Export default API client
export default apiClient;
