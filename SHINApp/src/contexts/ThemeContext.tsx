import React, {createContext, useContext, useEffect} from 'react';
import {useColorScheme} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {useAppDispatch, useAppSelector} from '../store';
import {setTheme, ThemeMode} from '../store/slices/themeSlice';

interface ThemeContextType {
  theme: ThemeMode;
  toggleTheme: () => void;
  colors: {
    background: string;
    surface: string;
    primary: string;
    secondary: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const lightColors = {
  background: '#ffffff',
  surface: '#f8fafc',
  primary: '#3b82f6',
  secondary: '#64748b',
  text: '#1e293b',
  textSecondary: '#64748b',
  border: '#e2e8f0',
  success: '#22c55e',
  warning: '#f59e0b',
  error: '#ef4444',
};

const darkColors = {
  background: '#0f172a',
  surface: '#1e293b',
  primary: '#60a5fa',
  secondary: '#94a3b8',
  text: '#f1f5f9',
  textSecondary: '#94a3b8',
  border: '#334155',
  success: '#4ade80',
  warning: '#fbbf24',
  error: '#f87171',
};

export const ThemeProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const dispatch = useAppDispatch();
  const theme = useAppSelector(state => state.theme.mode);
  const systemColorScheme = useColorScheme();

  useEffect(() => {
    loadTheme();
  }, []);

  useEffect(() => {
    saveTheme(theme);
  }, [theme]);

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme');
      if (savedTheme) {
        dispatch(setTheme(savedTheme as ThemeMode));
      } else {
        dispatch(setTheme(systemColorScheme === 'dark' ? 'dark' : 'light'));
      }
    } catch (error) {
      console.error('Error loading theme:', error);
    }
  };

  const saveTheme = async (themeMode: ThemeMode) => {
    try {
      await AsyncStorage.setItem('theme', themeMode);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    dispatch(setTheme(newTheme));
  };

  const colors = theme === 'light' ? lightColors : darkColors;

  const value: ThemeContextType = {
    theme,
    toggleTheme,
    colors,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
