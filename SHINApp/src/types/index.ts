// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  LanguageThemeSetup: undefined;
  PhoneLogin: undefined;
  VerificationCode: {phoneNumber: string};
};

export type MainTabParamList = {
  Dashboard: undefined;
  Building: undefined;
  Chats: undefined;
  Voting: undefined;
  Account: undefined;
};

export type MainStackParamList = {
  MainTabs: undefined;
  ElevatorCode: undefined;
  MaintenanceFee: undefined;
  PiggyBank: undefined;
  TodoList: undefined;
};

// User types
export interface User {
  id: string;
  name: string;
  phoneNumber: string;
  entrance: string;
  apartment: string;
  familyMembers: FamilyMember[];
}

export interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
}

// Building types
export interface Building {
  id: string;
  name: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  notes: string;
}

export interface ElevatorCode {
  id: string;
  code: string;
  entrance: string;
  lastUpdated: string;
}

export interface Contact {
  id: string;
  name: string;
  role: string;
  phoneNumber: string;
}

// Financial types
export interface MaintenanceFee {
  id: string;
  amount: number;
  dueDate: string;
  isPaid: boolean;
  goal: number;
  currentAmount: number;
}

export interface Expense {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
}

// Chat types
export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  mentions?: string[];
}

export interface ChatRoom {
  id: string;
  name: string;
  type: 'entrance' | 'building';
  participants: string[];
  lastMessage?: ChatMessage;
  unreadCount: number;
}

// Voting types
export interface Vote {
  id: string;
  title: string;
  description: string;
  options: VoteOption[];
  createdBy: string;
  createdAt: string;
  expiresAt: string;
  isActive: boolean;
  allowComments: boolean;
  oneVotePerApartment: boolean;
}

export interface VoteOption {
  id: string;
  text: string;
  votes: number;
}

export interface UserVote {
  id: string;
  voteId: string;
  optionId: string;
  userId: string;
  apartment: string;
  comment?: string;
  timestamp: string;
}

// Todo/Request types
export interface TodoRequest {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  images?: string[];
  assignedTo?: string;
  comments: TodoComment[];
}

export interface TodoComment {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  timestamp: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'announcement' | 'vote' | 'payment' | 'chat' | 'maintenance';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  data?: any;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Theme types
export type ThemeMode = 'light' | 'dark';

export interface ThemeColors {
  background: string;
  surface: string;
  primary: string;
  secondary: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

// Language types
export type Language = 'en' | 'ka' | 'ru';

// Utility types
export type Status = 'idle' | 'loading' | 'success' | 'error';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}
