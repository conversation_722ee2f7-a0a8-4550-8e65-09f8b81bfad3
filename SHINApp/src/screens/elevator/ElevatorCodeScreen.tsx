import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {Copy, RefreshCw, Key} from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';

interface ElevatorCode {
  id: string;
  code: string;
  entrance: string;
  lastUpdated: string;
}

export const ElevatorCodeScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data - in real app this would come from Redux store
  const [elevatorCodes] = useState<ElevatorCode[]>([
    {
      id: '1',
      code: '1234',
      entrance: 'Main Entrance',
      lastUpdated: '2024-06-12T10:30:00Z',
    },
    {
      id: '2',
      code: '5678',
      entrance: 'Side Entrance',
      lastUpdated: '2024-06-12T10:30:00Z',
    },
  ]);

  const handleCopyCode = async (code: string) => {
    await Clipboard.setStringAsync(code);
    Alert.alert(t('common.success'), t('common.copied'));
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    Alert.alert(t('common.success'), 'Elevator codes refreshed');
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Key size={32} color={colors.primary} />
          <Text style={[styles.title, {color: colors.text}]}>
            {t('elevator.code')}
          </Text>
          <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
            Current elevator access codes for your building
          </Text>
        </View>

        <View style={styles.codesContainer}>
          {elevatorCodes.map((elevatorCode) => (
            <Card key={elevatorCode.id} variant="elevated" style={styles.codeCard}>
              <View style={styles.cardHeader}>
                <Text style={[styles.entranceName, {color: colors.text}]}>
                  {elevatorCode.entrance}
                </Text>
                <TouchableOpacity
                  onPress={() => handleCopyCode(elevatorCode.code)}
                  style={[styles.copyButton, {backgroundColor: colors.primary}]}>
                  <Copy size={16} color="#ffffff" />
                </TouchableOpacity>
              </View>

              <View style={styles.codeDisplay}>
                <Text style={[styles.codeText, {color: colors.primary}]}>
                  {elevatorCode.code}
                </Text>
              </View>

              <Text style={[styles.lastUpdated, {color: colors.textSecondary}]}>
                {t('elevator.lastUpdated')}: {formatLastUpdated(elevatorCode.lastUpdated)}
              </Text>
            </Card>
          ))}
        </View>

        <View style={styles.actions}>
          <Button
            title={t('common.refresh')}
            onPress={handleRefresh}
            loading={isRefreshing}
            variant="outline"
            icon={<RefreshCw size={20} color={colors.primary} />}
            style={styles.refreshButton}
          />
        </View>

        <View style={styles.infoSection}>
          <Card variant="outlined" style={styles.infoCard}>
            <Text style={[styles.infoTitle, {color: colors.text}]}>
              Important Information
            </Text>
            <Text style={[styles.infoText, {color: colors.textSecondary}]}>
              • Elevator codes are updated automatically every month
            </Text>
            <Text style={[styles.infoText, {color: colors.textSecondary}]}>
              • Share codes only with authorized residents
            </Text>
            <Text style={[styles.infoText, {color: colors.textSecondary}]}>
              • Contact building management if codes don't work
            </Text>
          </Card>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  codesContainer: {
    marginBottom: 24,
  },
  codeCard: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  entranceName: {
    fontSize: 18,
    fontWeight: '600',
  },
  copyButton: {
    padding: 8,
    borderRadius: 8,
  },
  codeDisplay: {
    alignItems: 'center',
    marginBottom: 16,
  },
  codeText: {
    fontSize: 36,
    fontWeight: 'bold',
    letterSpacing: 8,
    fontFamily: 'monospace',
  },
  lastUpdated: {
    fontSize: 12,
    textAlign: 'center',
  },
  actions: {
    marginBottom: 24,
  },
  refreshButton: {
    width: '100%',
  },
  infoSection: {
    flex: 1,
  },
  infoCard: {
    padding: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
});
