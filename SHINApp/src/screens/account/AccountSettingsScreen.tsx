import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import {User, Edit3, LogOut, Users} from 'lucide-react-native';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {useAppDispatch} from '../../store';
import {logout} from '../../store/slices/authSlice';
import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';
import {Input} from '../../components/ui/Input';

interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
}

export const AccountSettingsScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();
  const dispatch = useAppDispatch();

  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState('<PERSON>');
  const [originalName] = useState('<PERSON>');

  // Mock data - in real app this would come from Redux store
  const userProfile = {
    phoneNumber: '+995 555 123 456',
    entrance: '2',
    apartment: '4B',
  };

  const familyMembers: FamilyMember[] = [
    {id: '1', name: 'Jane Doe', relationship: 'Spouse'},
    {id: '2', name: 'Alice Doe', relationship: 'Child'},
  ];

  const handleSaveChanges = () => {
    if (name.trim() === '') {
      Alert.alert(t('common.error'), 'Name cannot be empty');
      return;
    }

    // Validate name (letters, spaces, periods only)
    const nameRegex = /^[a-zA-Z\s.]+$/;
    if (!nameRegex.test(name)) {
      Alert.alert(t('common.error'), 'Name can only contain letters, spaces, and periods');
      return;
    }

    setIsEditing(false);
    Alert.alert(t('common.success'), t('account.changesSaved'));
  };

  const handleCancel = () => {
    setName(originalName);
    setIsEditing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      t('account.logout'),
      t('account.logoutConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('account.logout'),
          style: 'destructive',
          onPress: () => dispatch(logout()),
        },
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <User size={32} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              {t('account.settings')}
            </Text>
          </View>

          <Card variant="elevated" style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <Text style={[styles.sectionTitle, {color: colors.text}]}>
                {t('account.profile')}
              </Text>
              {!isEditing && (
                <Button
                  title={t('common.edit')}
                  onPress={() => setIsEditing(true)}
                  variant="ghost"
                  size="small"
                  icon={<Edit3 size={16} color={colors.primary} />}
                />
              )}
            </View>

            <View style={styles.profileContent}>
              {isEditing ? (
                <Input
                  label={t('account.name')}
                  value={name}
                  onChangeText={setName}
                  placeholder="Enter your name"
                  autoFocus
                />
              ) : (
                <View style={styles.fieldContainer}>
                  <Text style={[styles.fieldLabel, {color: colors.textSecondary}]}>
                    {t('account.name')}
                  </Text>
                  <Text style={[styles.fieldValue, {color: colors.text}]}>
                    {name}
                  </Text>
                </View>
              )}

              <View style={styles.fieldContainer}>
                <Text style={[styles.fieldLabel, {color: colors.textSecondary}]}>
                  Phone Number
                </Text>
                <Text style={[styles.fieldValue, {color: colors.text}]}>
                  {userProfile.phoneNumber}
                </Text>
              </View>

              <View style={styles.fieldRow}>
                <View style={[styles.fieldContainer, {flex: 1, marginRight: 12}]}>
                  <Text style={[styles.fieldLabel, {color: colors.textSecondary}]}>
                    {t('account.entrance')}
                  </Text>
                  <Text style={[styles.fieldValue, {color: colors.text}]}>
                    {userProfile.entrance}
                  </Text>
                </View>

                <View style={[styles.fieldContainer, {flex: 1, marginLeft: 12}]}>
                  <Text style={[styles.fieldLabel, {color: colors.textSecondary}]}>
                    {t('account.apartment')}
                  </Text>
                  <Text style={[styles.fieldValue, {color: colors.text}]}>
                    {userProfile.apartment}
                  </Text>
                </View>
              </View>
            </View>

            {isEditing && (
              <View style={styles.editActions}>
                <Button
                  title={t('common.cancel')}
                  onPress={handleCancel}
                  variant="outline"
                  style={styles.actionButton}
                />
                <Button
                  title={t('account.saveChanges')}
                  onPress={handleSaveChanges}
                  style={styles.actionButton}
                />
              </View>
            )}
          </Card>

          <Card variant="elevated" style={styles.familyCard}>
            <View style={styles.familyHeader}>
              <Users size={24} color={colors.primary} />
              <Text style={[styles.sectionTitle, {color: colors.text}]}>
                {t('account.familyMembers')}
              </Text>
            </View>

            <View style={styles.familyList}>
              {familyMembers.map((member) => (
                <View key={member.id} style={styles.familyMember}>
                  <View>
                    <Text style={[styles.memberName, {color: colors.text}]}>
                      {member.name}
                    </Text>
                    <Text style={[styles.memberRelationship, {color: colors.textSecondary}]}>
                      {member.relationship}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </Card>

          <View style={styles.logoutSection}>
            <Button
              title={t('account.logout')}
              onPress={handleLogout}
              variant="outline"
              icon={<LogOut size={20} color={colors.error} />}
              style={{...styles.logoutButton, borderColor: colors.error}}
              textStyle={{color: colors.error}}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  profileContent: {
    marginBottom: 16,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldRow: {
    flexDirection: 'row',
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  fieldValue: {
    fontSize: 16,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  familyCard: {
    marginBottom: 20,
  },
  familyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  familyList: {
    gap: 12,
  },
  familyMember: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500',
  },
  memberRelationship: {
    fontSize: 14,
  },
  logoutSection: {
    marginTop: 20,
  },
  logoutButton: {
    width: '100%',
  },
});
