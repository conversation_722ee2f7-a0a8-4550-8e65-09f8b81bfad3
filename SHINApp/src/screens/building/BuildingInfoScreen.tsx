import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {Building, MapPin, Phone} from 'lucide-react-native';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';

export const BuildingInfoScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Building size={32} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              {t('building.info')}
            </Text>
          </View>

          <Card variant="elevated" style={styles.infoCard}>
            <View style={styles.addressSection}>
              <MapPin size={24} color={colors.primary} />
              <View style={styles.addressText}>
                <Text style={[styles.addressTitle, {color: colors.text}]}>
                  Residential Complex "Saburtalo Gardens"
                </Text>
                <Text style={[styles.address, {color: colors.textSecondary}]}>
                  123 Pekini Avenue, Saburtalo District
                </Text>
                <Text style={[styles.address, {color: colors.textSecondary}]}>
                  Tbilisi 0177, Georgia
                </Text>
              </View>
            </View>
          </Card>

          <Card variant="elevated" style={styles.contactsCard}>
            <Text style={[styles.sectionTitle, {color: colors.text}]}>
              {t('building.contacts')}
            </Text>
            
            <View style={styles.contactItem}>
              <Phone size={20} color={colors.primary} />
              <View style={styles.contactText}>
                <Text style={[styles.contactName, {color: colors.text}]}>
                  Building Chairman
                </Text>
                <Text style={[styles.contactPhone, {color: colors.textSecondary}]}>
                  +995 555 123 456
                </Text>
              </View>
            </View>

            <View style={styles.contactItem}>
              <Phone size={20} color={colors.primary} />
              <View style={styles.contactText}>
                <Text style={[styles.contactName, {color: colors.text}]}>
                  Security
                </Text>
                <Text style={[styles.contactPhone, {color: colors.textSecondary}]}>
                  +995 555 789 012
                </Text>
              </View>
            </View>
          </Card>

          <Card variant="outlined" style={styles.notesCard}>
            <Text style={[styles.sectionTitle, {color: colors.text}]}>
              Personal Notes
            </Text>
            <Text style={[styles.notesText, {color: colors.textSecondary}]}>
              Tap to add your personal notes about the building...
            </Text>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  infoCard: {
    marginBottom: 20,
  },
  addressSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressText: {
    marginLeft: 16,
    flex: 1,
  },
  addressTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  address: {
    fontSize: 14,
    lineHeight: 20,
  },
  contactsCard: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  contactText: {
    marginLeft: 12,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
  },
  contactPhone: {
    fontSize: 14,
  },
  notesCard: {
    marginBottom: 20,
  },
  notesText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});
