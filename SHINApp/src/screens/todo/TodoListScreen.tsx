import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import {CheckSquare} from 'lucide-react-native';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';

export const TodoListScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <CheckSquare size={32} color={colors.primary} />
          <Text style={[styles.title, {color: colors.text}]}>
            {t('todo.myRequests')}
          </Text>
        </View>

        <Card variant="elevated" style={styles.comingSoonCard}>
          <Text style={[styles.comingSoonTitle, {color: colors.text}]}>
            Coming Soon
          </Text>
          <Text style={[styles.comingSoonText, {color: colors.textSecondary}]}>
            Request submission functionality will be available in the next update.
          </Text>
        </Card>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  comingSoonCard: {
    alignItems: 'center',
    padding: 40,
  },
  comingSoonTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  comingSoonText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
