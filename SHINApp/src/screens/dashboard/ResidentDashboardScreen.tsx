import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {
  Bell,
  Vote,
  MessageCircle,
  CreditCard,
  Zap,
  Key,
  Building,
  PiggyBank,
  ChevronRight,
} from 'lucide-react-native';

import {MainStackParamList} from '../../navigation/MainNavigator';
import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';

type ResidentDashboardScreenNavigationProp = StackNavigationProp<
  MainStackParamList,
  'MainTabs'
>;

interface Props {
  navigation: ResidentDashboardScreenNavigationProp;
}

interface DashboardCardProps {
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  onPress: () => void;
  badge?: number;
  status?: 'success' | 'warning' | 'error';
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  subtitle,
  icon,
  onPress,
  badge,
  status,
}) => {
  const {colors} = useTheme();

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'error':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <Card onPress={onPress} variant="elevated" style={styles.dashboardCard}>
      <View style={styles.cardContent}>
        <View style={styles.cardIcon}>
          {icon}
          {badge && badge > 0 && (
            <View style={[styles.badge, {backgroundColor: colors.error}]}>
              <Text style={styles.badgeText}>{badge > 99 ? '99+' : badge}</Text>
            </View>
          )}
        </View>
        <View style={styles.cardText}>
          <Text style={[styles.cardTitle, {color: colors.text}]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.cardSubtitle, {color: getStatusColor()}]}>
              {subtitle}
            </Text>
          )}
        </View>
        <ChevronRight size={20} color={colors.textSecondary} />
      </View>
    </Card>
  );
};

export const ResidentDashboardScreen: React.FC<Props> = ({navigation}) => {
  const {colors} = useTheme();
  const {t} = useLanguage();

  const dashboardItems = [
    {
      title: t('dashboard.announcements'),
      subtitle: '3 new announcements',
      icon: <Bell size={24} color={colors.primary} />,
      onPress: () => {},
      badge: 3,
    },
    {
      title: t('dashboard.activeVotes'),
      subtitle: '2 votes pending',
      icon: <Vote size={24} color={colors.primary} />,
      onPress: () => {},
      badge: 2,
    },
    {
      title: t('dashboard.unreadMessages'),
      subtitle: '5 unread messages',
      icon: <MessageCircle size={24} color={colors.primary} />,
      onPress: () => {},
      badge: 5,
    },
    {
      title: t('dashboard.elevatorCode'),
      subtitle: 'Code: 1234',
      icon: <Key size={24} color={colors.primary} />,
      onPress: () => navigation.navigate('ElevatorCode'),
    },
    {
      title: t('dashboard.maintenanceFee'),
      subtitle: 'Due: 150 GEL',
      icon: <CreditCard size={24} color={colors.primary} />,
      onPress: () => navigation.navigate('MaintenanceFee'),
      status: 'warning' as const,
    },
    {
      title: t('dashboard.piggyBank'),
      subtitle: 'Saved: 2,450 GEL',
      icon: <PiggyBank size={24} color={colors.primary} />,
      onPress: () => navigation.navigate('PiggyBank'),
      status: 'success' as const,
    },
    {
      title: t('dashboard.buildingInfo'),
      subtitle: 'View details',
      icon: <Building size={24} color={colors.primary} />,
      onPress: () => {},
    },
    {
      title: t('dashboard.utilityOutages'),
      subtitle: 'No outages',
      icon: <Zap size={24} color={colors.primary} />,
      onPress: () => {},
      status: 'success' as const,
    },
  ];

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={[styles.greeting, {color: colors.text}]}>
            {t('dashboard.welcome')}, John! 👋
          </Text>
          <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
            Apartment 4B, Entrance 2
          </Text>
        </View>

        <View style={styles.content}>
          {dashboardItems.map((item, index) => (
            <DashboardCard
              key={index}
              title={item.title}
              subtitle={item.subtitle}
              icon={item.icon}
              onPress={item.onPress}
              badge={item.badge}
              status={item.status}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  dashboardCard: {
    marginBottom: 12,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIcon: {
    position: 'relative',
    marginRight: 16,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  cardSubtitle: {
    fontSize: 14,
  },
});
