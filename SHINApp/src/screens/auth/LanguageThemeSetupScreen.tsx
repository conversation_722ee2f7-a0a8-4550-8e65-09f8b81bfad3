import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {Sun, Moon, Globe} from 'lucide-react-native';

import {AuthStackParamList} from '../../navigation/AuthNavigator';
import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Language} from '../../store/slices/languageSlice';
import {Button} from '../../components/ui/Button';
import {Card} from '../../components/ui/Card';

type LanguageThemeSetupScreenNavigationProp = StackNavigationProp<
  AuthStackParamList,
  'LanguageThemeSetup'
>;

interface Props {
  navigation: LanguageThemeSetupScreenNavigationProp;
}

export const LanguageThemeSetupScreen: React.FC<Props> = ({navigation}) => {
  const {theme, toggleTheme, colors} = useTheme();
  const {language, setLanguage, t} = useLanguage();

  const languages = [
    {code: 'en' as Language, name: 'English', flag: '🇺🇸'},
    {code: 'ka' as Language, name: 'ქართული', flag: '🇬🇪'},
    {code: 'ru' as Language, name: 'Русский', flag: '🇷🇺'},
  ];

  const handleContinue = () => {
    navigation.navigate('PhoneLogin');
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Globe size={48} color={colors.primary} />
          <Text style={[styles.title, {color: colors.text}]}>
            Welcome to SHIN
          </Text>
          <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
            Choose your language and theme preferences
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, {color: colors.text}]}>
            {t('settings.language')}
          </Text>
          <View style={styles.languageGrid}>
            {languages.map((lang) => (
              <TouchableOpacity
                key={lang.code}
                style={[
                  styles.languageOption,
                  {
                    backgroundColor: colors.surface,
                    borderColor: language === lang.code ? colors.primary : colors.border,
                    borderWidth: 2,
                  },
                ]}
                onPress={() => setLanguage(lang.code)}>
                <Text style={styles.flag}>{lang.flag}</Text>
                <Text style={[styles.languageName, {color: colors.text}]}>
                  {lang.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, {color: colors.text}]}>
            {t('settings.theme')}
          </Text>
          <View style={styles.themeOptions}>
            <TouchableOpacity
              style={[
                styles.themeOption,
                {
                  backgroundColor: colors.surface,
                  borderColor: theme === 'light' ? colors.primary : colors.border,
                  borderWidth: 2,
                },
              ]}
              onPress={() => theme === 'dark' && toggleTheme()}>
              <Sun size={24} color={theme === 'light' ? colors.primary : colors.textSecondary} />
              <Text style={[styles.themeText, {color: colors.text}]}>
                {t('settings.light')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeOption,
                {
                  backgroundColor: colors.surface,
                  borderColor: theme === 'dark' ? colors.primary : colors.border,
                  borderWidth: 2,
                },
              ]}
              onPress={() => theme === 'light' && toggleTheme()}>
              <Moon size={24} color={theme === 'dark' ? colors.primary : colors.textSecondary} />
              <Text style={[styles.themeText, {color: colors.text}]}>
                {t('settings.dark')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.footer}>
          <Button
            title={t('common.next')}
            onPress={handleContinue}
            size="large"
            style={styles.continueButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  languageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  languageOption: {
    flex: 1,
    minWidth: 100,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  flag: {
    fontSize: 24,
    marginBottom: 8,
  },
  languageName: {
    fontSize: 14,
    fontWeight: '500',
  },
  themeOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  themeOption: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  themeText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  footer: {
    marginTop: 'auto',
    paddingBottom: 32,
  },
  continueButton: {
    width: '100%',
  },
});
