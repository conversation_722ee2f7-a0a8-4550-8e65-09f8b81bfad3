import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import {Shield} from 'lucide-react-native';

import {AuthStackParamList} from '../../navigation/AuthNavigator';
import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {useAppDispatch, useAppSelector} from '../../store';
import {
  setVerificationCode,
  setLoading,
  setError,
  setResendCooldown,
  loginSuccess,
} from '../../store/slices/authSlice';
import {Button} from '../../components/ui/Button';

type VerificationCodeScreenNavigationProp = StackNavigationProp<
  AuthStackParamList,
  'VerificationCode'
>;

type VerificationCodeScreenRouteProp = RouteProp<
  AuthStackParamList,
  'VerificationCode'
>;

interface Props {
  navigation: VerificationCodeScreenNavigationProp;
  route: VerificationCodeScreenRouteProp;
}

export const VerificationCodeScreen: React.FC<Props> = ({navigation, route}) => {
  const {colors} = useTheme();
  const {t} = useLanguage();
  const dispatch = useAppDispatch();
  const {isLoading, error, resendCooldown} = useAppSelector(state => state.auth);
  
  const {phoneNumber} = route.params;
  const [code, setCode] = useState(['', '', '', '']);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    // Start resend cooldown
    dispatch(setResendCooldown(60));
    const interval = setInterval(() => {
      dispatch(setResendCooldown(Math.max(0, resendCooldown - 1)));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Auto-verify when all digits are entered
    if (code.every(digit => digit !== '')) {
      handleVerifyCode();
    }
  }, [code]);

  const handleCodeChange = (text: string, index: number) => {
    if (text.length > 1) {
      // Handle paste
      const pastedCode = text.slice(0, 4).split('');
      const newCode = [...code];
      pastedCode.forEach((digit, i) => {
        if (i < 4) {
          newCode[i] = digit;
        }
      });
      setCode(newCode);
      
      // Focus last filled input or next empty
      const nextIndex = Math.min(pastedCode.length, 3);
      inputRefs.current[nextIndex]?.focus();
    } else {
      // Handle single digit
      const newCode = [...code];
      newCode[index] = text;
      setCode(newCode);

      // Auto-advance to next input
      if (text && index < 3) {
        inputRefs.current[index + 1]?.focus();
      }
    }

    if (error) {
      dispatch(setError(null));
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyCode = async () => {
    const verificationCode = code.join('');
    
    if (verificationCode.length !== 4) {
      dispatch(setError(t('auth.invalidCode')));
      return;
    }

    try {
      dispatch(setLoading(true));
      dispatch(setError(null));
      dispatch(setVerificationCode(verificationCode));
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, accept any 4-digit code
      if (verificationCode === '0000') {
        dispatch(setError(t('auth.invalidCode')));
        return;
      }
      
      // Simulate successful login
      dispatch(loginSuccess('mock-jwt-token'));
    } catch (err) {
      dispatch(setError('Verification failed'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleResendCode = async () => {
    if (resendCooldown > 0) return;

    try {
      dispatch(setLoading(true));
      dispatch(setError(null));
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset cooldown
      dispatch(setResendCooldown(60));
      
      // Clear current code
      setCode(['', '', '', '']);
      inputRefs.current[0]?.focus();
      
      Alert.alert('Success', 'Verification code sent successfully');
    } catch (err) {
      dispatch(setError('Failed to resend code'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const formatPhoneNumber = (phone: string) => {
    // Format +995XXXXXXXXX to +995 XXX XXX XXX
    if (phone.startsWith('+995')) {
      const number = phone.slice(4);
      return `+995 ${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6)}`;
    }
    return phone;
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Shield size={48} color={colors.primary} />
          <Text style={[styles.title, {color: colors.text}]}>
            {t('auth.verificationCode')}
          </Text>
          <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
            {t('auth.enterCode')}
          </Text>
          <Text style={[styles.phoneNumber, {color: colors.text}]}>
            {formatPhoneNumber(phoneNumber)}
          </Text>
        </View>

        <View style={styles.codeContainer}>
          {code.map((digit, index) => (
            <TextInput
              key={index}
              ref={ref => (inputRefs.current[index] = ref)}
              style={[
                styles.codeInput,
                {
                  backgroundColor: colors.surface,
                  borderColor: digit ? colors.primary : colors.border,
                  color: colors.text,
                },
              ]}
              value={digit}
              onChangeText={text => handleCodeChange(text, index)}
              onKeyPress={({nativeEvent}) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="number-pad"
              maxLength={1}
              selectTextOnFocus
              autoFocus={index === 0}
            />
          ))}
        </View>

        {error && (
          <Text style={[styles.errorText, {color: colors.error}]}>
            {error}
          </Text>
        )}

        <View style={styles.resendContainer}>
          <TouchableOpacity
            onPress={handleResendCode}
            disabled={resendCooldown > 0 || isLoading}
            style={styles.resendButton}>
            <Text
              style={[
                styles.resendText,
                {
                  color: resendCooldown > 0 ? colors.textSecondary : colors.primary,
                },
              ]}>
              {resendCooldown > 0
                ? `${t('auth.resendIn')} ${resendCooldown} ${t('auth.seconds')}`
                : t('auth.resendCode')}
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title={t('common.confirm')}
          onPress={handleVerifyCode}
          loading={isLoading}
          disabled={code.some(digit => !digit)}
          size="large"
          style={styles.verifyButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 8,
  },
  phoneNumber: {
    fontSize: 18,
    fontWeight: '600',
  },
  codeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  codeInput: {
    width: 56,
    height: 56,
    borderWidth: 2,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
  },
  errorText: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 16,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  resendButton: {
    padding: 8,
  },
  resendText: {
    fontSize: 16,
    fontWeight: '500',
  },
  verifyButton: {
    width: '100%',
    marginTop: 'auto',
    marginBottom: 32,
  },
});
