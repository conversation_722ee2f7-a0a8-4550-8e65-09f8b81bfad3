import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {Phone} from 'lucide-react-native';

import {AuthStackParamList} from '../../navigation/AuthNavigator';
import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {useAppDispatch, useAppSelector} from '../../store';
import {setPhoneNumber, setLoading, setError} from '../../store/slices/authSlice';
import {Button} from '../../components/ui/Button';
import {Input} from '../../components/ui/Input';

type PhoneLoginScreenNavigationProp = StackNavigationProp<
  AuthStackParamList,
  'PhoneLogin'
>;

interface Props {
  navigation: PhoneLoginScreenNavigationProp;
}

export const PhoneLoginScreen: React.FC<Props> = ({navigation}) => {
  const {colors} = useTheme();
  const {t} = useLanguage();
  const dispatch = useAppDispatch();
  const {isLoading, error} = useAppSelector(state => state.auth);
  
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+995');

  const validatePhoneNumber = (phoneNumber: string): boolean => {
    // Basic validation for Georgian phone numbers
    const cleanPhone = phoneNumber.replace(/\s+/g, '');
    return cleanPhone.length >= 9 && /^\d+$/.test(cleanPhone);
  };

  const handleSendCode = async () => {
    if (!validatePhoneNumber(phone)) {
      dispatch(setError(t('auth.invalidPhone')));
      return;
    }

    const fullPhoneNumber = `${countryCode}${phone}`;
    
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));
      dispatch(setPhoneNumber(fullPhoneNumber));
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Navigate to verification screen
      navigation.navigate('VerificationCode', {phoneNumber: fullPhoneNumber});
    } catch (err) {
      dispatch(setError('Failed to send verification code'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const formatPhoneNumber = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    
    // Format as XXX XXX XXX
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
    } else {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)}`;
    }
  };

  const handlePhoneChange = (text: string) => {
    const formatted = formatPhoneNumber(text);
    setPhone(formatted);
    if (error) {
      dispatch(setError(null));
    }
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Phone size={48} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              {t('auth.phoneLogin')}
            </Text>
            <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
              {t('auth.enterPhone')}
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.phoneInputContainer}>
              <View style={[styles.countryCodeContainer, {backgroundColor: colors.surface, borderColor: colors.border}]}>
                <Text style={[styles.countryCode, {color: colors.text}]}>
                  🇬🇪 {countryCode}
                </Text>
              </View>
              <Input
                value={phone}
                onChangeText={handlePhoneChange}
                placeholder="XXX XXX XXX"
                keyboardType="phone-pad"
                maxLength={11} // Formatted length
                containerStyle={styles.phoneInput}
                error={error || undefined}
              />
            </View>

            <Button
              title={t('auth.sendCode')}
              onPress={handleSendCode}
              loading={isLoading}
              disabled={!phone || phone.length < 9}
              size="large"
              style={styles.sendButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={[styles.footerText, {color: colors.textSecondary}]}>
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  countryCodeContainer: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    minHeight: 48,
    justifyContent: 'center',
  },
  countryCode: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneInput: {
    flex: 1,
    marginBottom: 0,
  },
  sendButton: {
    width: '100%',
  },
  footer: {
    paddingBottom: 32,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});
