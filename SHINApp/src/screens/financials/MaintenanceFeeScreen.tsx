import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {CreditCard, TrendingUp} from 'lucide-react-native';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';

export const MaintenanceFeeScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();

  const currentFee = {
    amount: 150,
    dueDate: '2024-06-30',
    isPaid: false,
    goal: 5000,
    currentAmount: 3200,
  };

  const progress = (currentFee.currentAmount / currentFee.goal) * 100;

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <CreditCard size={32} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              {t('financials.maintenanceFee')}
            </Text>
          </View>

          <Card variant="elevated" style={styles.feeCard}>
            <View style={styles.feeHeader}>
              <Text style={[styles.feeAmount, {color: colors.text}]}>
                {currentFee.amount} GEL
              </Text>
              <View style={[
                styles.statusBadge,
                {backgroundColor: currentFee.isPaid ? colors.success : colors.warning}
              ]}>
                <Text style={styles.statusText}>
                  {currentFee.isPaid ? t('financials.paid') : t('financials.unpaid')}
                </Text>
              </View>
            </View>
            
            <Text style={[styles.dueDate, {color: colors.textSecondary}]}>
              Due: June 30, 2024
            </Text>

            {!currentFee.isPaid && (
              <Button
                title={t('financials.payNow')}
                onPress={() => {}}
                style={styles.payButton}
              />
            )}
          </Card>

          <Card variant="elevated" style={styles.progressCard}>
            <View style={styles.progressHeader}>
              <TrendingUp size={24} color={colors.primary} />
              <Text style={[styles.progressTitle, {color: colors.text}]}>
                {t('financials.currentGoal')}
              </Text>
            </View>

            <View style={styles.progressInfo}>
              <Text style={[styles.progressAmount, {color: colors.text}]}>
                {currentFee.currentAmount} / {currentFee.goal} GEL
              </Text>
              <Text style={[styles.progressPercent, {color: colors.primary}]}>
                {Math.round(progress)}%
              </Text>
            </View>

            <View style={[styles.progressBar, {backgroundColor: colors.border}]}>
              <View
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: colors.primary,
                    width: `${progress}%`,
                  },
                ]}
              />
            </View>
          </Card>

          <Card variant="outlined" style={styles.historyCard}>
            <Text style={[styles.sectionTitle, {color: colors.text}]}>
              {t('financials.paymentHistory')}
            </Text>
            <Text style={[styles.comingSoonText, {color: colors.textSecondary}]}>
              Payment history will be available soon.
            </Text>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  feeCard: {
    marginBottom: 20,
  },
  feeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  feeAmount: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  dueDate: {
    fontSize: 14,
    marginBottom: 16,
  },
  payButton: {
    width: '100%',
  },
  progressCard: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressAmount: {
    fontSize: 16,
    fontWeight: '500',
  },
  progressPercent: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  historyCard: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  comingSoonText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});
