import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {PiggyBank} from 'lucide-react-native';

import {useTheme} from '../../contexts/ThemeContext';
import {useLanguage} from '../../contexts/LanguageContext';
import {Card} from '../../components/ui/Card';

export const PiggyBankScreen: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useLanguage();

  const totalSaved = 2450;

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <PiggyBank size={32} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              {t('financials.piggyBank')}
            </Text>
          </View>

          <Card variant="elevated" style={styles.totalCard}>
            <Text style={[styles.totalLabel, {color: colors.textSecondary}]}>
              {t('financials.totalSaved')}
            </Text>
            <Text style={[styles.totalAmount, {color: colors.success}]}>
              {totalSaved} GEL
            </Text>
          </Card>

          <Card variant="outlined" style={styles.expensesCard}>
            <Text style={[styles.sectionTitle, {color: colors.text}]}>
              {t('financials.buildingExpenses')}
            </Text>
            <Text style={[styles.comingSoonText, {color: colors.textSecondary}]}>
              Building expense history will be available soon.
            </Text>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  totalCard: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 32,
  },
  totalLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  expensesCard: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  comingSoonText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});
