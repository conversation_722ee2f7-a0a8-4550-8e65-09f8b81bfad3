{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "skipLibCheck": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/hooks/*": ["hooks/*"], "@/store/*": ["store/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/constants/*": ["constants/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*", "index.js", "App.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}