{"name": "SHINApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^2.0.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.2", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.0.4", "@react-native-async-storage/async-storage": "^1.21.0", "react-native-toast-message": "^2.2.0", "lottie-react-native": "^6.4.1", "@react-native-clipboard/clipboard": "^1.13.2", "react-native-push-notification": "^8.1.1", "react-native-permissions": "^4.1.1", "react-native-device-info": "^10.13.0", "react-native-svg": "^14.1.0", "nativewind": "^2.0.11", "lucide-react-native": "^0.294.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.8.8", "react-test-renderer": "18.2.0", "typescript": "^5.0.4", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18"}}