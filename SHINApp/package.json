{"name": "SHINApp", "version": "0.0.1", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "babel-preset-expo": "~11.0.0", "expo": "^53.0.11", "expo-font": "~12.0.0", "expo-status-bar": "~2.0.0", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.514.0", "nativewind": "^2.0.11", "react": "^19.0.0", "react-native": "0.79.3", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "^2.25.0", "react-native-permissions": "^5.4.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "@react-native/eslint-config": "^0.79.3", "@react-native/metro-config": "^0.79.3", "@react-native/typescript-config": "^0.79.3", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^19.1.0", "babel-jest": "^30.0.0", "eslint": "^9.28.0", "jest": "^30.0.0", "metro-react-native-babel-preset": "0.77.0", "prettier": "^3.5.3", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}