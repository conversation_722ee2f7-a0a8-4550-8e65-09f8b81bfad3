import React from 'react';
import {StatusBar} from 'react-native';
import {Provider} from 'react-redux';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import {store} from './src/store';
import {AppNavigator} from './src/navigation/AppNavigator';
import {ThemeProvider} from './src/contexts/ThemeContext';
import {LanguageProvider} from './src/contexts/LanguageContext';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <LanguageProvider>
          <GestureHandlerRootView style={{flex: 1}}>
            <NavigationContainer>
              <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
              <AppNavigator />
              <Toast />
            </NavigationContainer>
          </GestureHandlerRootView>
        </LanguageProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
